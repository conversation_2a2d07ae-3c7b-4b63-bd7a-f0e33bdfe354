<script lang="ts">
    import { t } from '$lib/stores/i18n';
	import {
		Card,
		Badge,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
        Button,
        Dropdown,
        DropdownItem,
        Tooltip
	} from 'flowbite-svelte';
    import { ChevronDownOutline } from "flowbite-svelte-icons";
	import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
	import {
		UserCircleOutline,
		BriefcaseOutline,
		ClockOutline,
		TicketOutline,
		EditSolid,
        DotsVerticalOutline,
		ArrowLeftOutline,
        UserSolid
	} from 'flowbite-svelte-icons';
	import type { PageData } from './$types';

	// Keep all original components
	import UserEdit from '$lib/components/UI/UserEdit.svelte';
	import UserDelete from '$lib/components/UI/UserDelete.svelte';
	import UserAssignPartner from '$lib/components/UI/UserAssignPartner.svelte';
	// import UserRemovePartner from '$lib/components/UI/UserRemovePartner.svelte';
	import UserAssignRole from '$lib/components/UI/UserAssignRole.svelte';
	// import UserRemoveRole from '$lib/components/UI/UserRemoveRole.svelte';
	import AssignLineAccount from '$src/lib/components/UI/AssignLineAccount.svelte';
	import UserAssignDepartment from '$src/lib/components/UI/UserAssignDepartment.svelte';
    import UserAssignTag from '$src/lib/components/UI/UserAssignTag.svelte';
    import { 
        formatTimestamp,
        displayDate, 
        timeAgo, 
        getStatusClass, 
        getPriorityClass, 
        getSentimentClass, 
        getSentimentIcon 
    } from '$lib/utils';

	export let data: PageData;
	$: ({ 
        user, 
        lineAccounts,
        partners, 
        role, 
        loginUser, 
        roles, 
        myTickets, 
        tickets, 
        departments,
        tags
    } = data);
    
    // Sorted roles by priority
    const priorityDict = {'System': 0,
        'Admin': -1,
        'Supervisor': -2,
        'Agent': -3,
    }

    // role: the role of the logged-in user
    // user?.roles: the roles of the user being viewed
	$: isAdmin = (role === 'Admin');
	$: isSupervisor = (role === 'Supervisor');
	$: editAllowed = (isAdmin || isSupervisor) && (priorityDict[role] > priorityDict[user?.roles]);

	// Status configuration
	const statusConfig = {
		online: { color: 'green', label: 'Online' },
		offline: { color: 'red', label: 'Offline' },
		away: { color: 'yellow', label: 'Away' }
	};

	// Filter tickets for current user
	$: userTickets =
		tickets?.filter((ticket) => ticket.owner.username === `${user.first_name} ${user.last_name}`) || [];

    // Pre-compute status colors for use in the template
    $: userStatusInfo = statusConfig[user.status] || {color: 'gray', label: 'Unknown'};
    
    // Determine if user has an image
    $: hasUserImage = user && user.image_url && user.image_url.length > 0;
</script>

<svelte:head>
	<title>Customers</title>
</svelte:head>


<div class="min-h-screen bg-gray-50 p-2">
    <div class="max-w-7xl mx-auto py-10">
        <!-- Back button and breadcrumb -->
        <div class="mb-6 flex items-center">
            <Breadcrumb>
                <BreadcrumbItem href="/" home>
                    <span class="text-gray-400">{t('home')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem href="/users">
                    <span class="text-gray-400">{t('users')}</span>
                </BreadcrumbItem>
                <BreadcrumbItem>
                    <span class="text-gray-700">{t('detail')}</span>
                </BreadcrumbItem>
            </Breadcrumb>
        </div>

        <div class="grid grid-cols-1 gap-6 lg:grid-cols-5">
            <!-- Left column - User information (2/5 width) -->
            <div class="lg:col-span-1">
                <!-- User header with photo and basic info -->
                <div class="mb-6 flex items-start">
                    <div class="relative mr-4">
                        <!-- User avatar with status indicator -->
                        <div class="h-16 w-16 overflow-hidden rounded-full bg-gray-200 relative">
                            {#if hasUserImage}
                                <img src="{user.image_url}" alt="{user.first_name} {user.last_name}" class="h-full w-full object-cover" />
                            {:else}
                                <!-- Show grey circle with initials if no image -->
                                <div class="h-full w-full flex items-center justify-center bg-gray-300 text-gray-700 font-medium text-xl">
                                    {user.first_name ? user.first_name[0] : ''}{user.last_name ? user.last_name[0] : ''}
                                </div>
                            {/if}
                        </div>
                        
                        <!-- Status indicator pin -->
                        <div
                            class={
                            `absolute bottom-0 right-0 h-4 w-4 rounded-full border-2 border-white
                                bg-${userStatusInfo.color}-500`
                            }
                            aria-label={userStatusInfo.label}
                            title={userStatusInfo.label}
                        ></div>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-normal">
                            <div class="max-w-[calc(100%)]">
                                <h1 
                                    class="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl 
                                        font-bold text-gray-900 
                                        truncate break-words overflow-hidden whitespace-normal max-w-full">
                                    {user.first_name} {user.last_name} ({user.name})
                                </h1>
                                <div class="flex items-center gap-2 mt-2">
                                    {#if user.status === 'online'}
                                        <Badge color="green">Online</Badge>
                                    {:else if user.status === 'away'}
                                        <Badge color="yellow">Away</Badge>
                                    {:else if user.status === 'offline'}
                                        <Badge color="red">Offline</Badge>
                                    {:else}
                                        <Badge color="red">Offline</Badge>
                                    {/if}
                                    {#if user.roles !== 'System'}
                                        <Badge color="blue">{user.roles}</Badge>
                                    {/if}
                                </div>
                            </div>
                            
                            <!-- {#if user.roles !== 'System' && editAllowed}
                                <div class="flex gap-2 ml-2">
                                    <Button color="none" class="p-1 hover:bg-gray-100">
                                        <DotsVerticalOutline class="h-6 w-6" />
                                        <Dropdown simple>
                                            <DropdownItem><UserEdit {user} /></DropdownItem>
                                            <DropdownItem><UserAssignPartner {user} {partners} /></DropdownItem>
                                            <DropdownItem><UserAssignTag {user} {tags} /></DropdownItem>
                                            <DropdownItem><UserAssignDepartment {user} {departments} /></DropdownItem>
                                            <DropdownItem><AssignLineAccount {user} {lineAccounts} /> </DropdownItem>
                                            {#if role === 'Admin' && (priorityDict[role] > priorityDict[user.roles])}
                                                <DropdownItem> <UserAssignRole {user} {roles} /> </DropdownItem>
                                            {/if}
                                            {#if priorityDict[role] > priorityDict[user.roles]}
                                                <DropdownItem> <UserDelete {user} /> </DropdownItem>
                                            {/if}
                                        </Dropdown>
                                    </Button>
                                </div>
                            {/if} -->
                        </div>
                    </div>
                </div>

                <!-- User details in sections -->
                <div class="rounded-lg">
                    <!-- Information -->
                    <div class="card bg-white border rounded-lg shadow-sm p-6 max-w-lg mx-auto mb-6">
                        <div class="header mb-4">
                            <div class="flex items-center justify-between">
                                <div class="title text-xl font-semibold text-gray-900">
                                    {t('user_profile')}
                                </div>
                                {#if user.roles !== 'System' && editAllowed}
                                    <Button color="light" size="xs" class="flex items-center gap-0 py-1">
                                        <Dropdown triggeredBy="#editDropdownButton">
                                            <DropdownItem><UserEdit {user} /></DropdownItem>
                                            <DropdownItem><UserAssignPartner {user} {partners} /></DropdownItem>
                                            <DropdownItem><UserAssignTag {user} {tags} /></DropdownItem>
                                            <DropdownItem><UserAssignDepartment {user} {departments} /></DropdownItem>
                                            <DropdownItem><AssignLineAccount {user} {lineAccounts} /> </DropdownItem>
                                            {#if role === 'Admin' && (priorityDict[role] > priorityDict[user.roles])}
                                                <DropdownItem> <UserAssignRole {user} {roles} /> </DropdownItem>
                                            {/if}
                                            {#if priorityDict[role] > priorityDict[user.roles]}
                                                <DropdownItem> <UserDelete {user} /> </DropdownItem>
                                            {/if}
                                        </Dropdown>
                                        <div id="editDropdownButton" class="flex items-center gap-0 py-0">
                                            {t('user_edit_menu')}
                                        </div>
                                    </Button>
                                {/if}
                            </div>
                            <div class="subtext text-sm text-gray-600 mt-3">{t("view_users_profile_memberships")}</div>
                        </div>
                    
                        <!-- <div class="org-item flex items-center justify-between border-t pt-4 pb-4 border-gray-200"> -->
                        <div class="border-t pt-4 pb-4 border-gray-200">
                            <div class="grid grid-cols-1 gap-y-3 sm:grid-cols-1">
                                <div>
                                    <div class="text-sm text-gray-500">{t('user_number')}</div>
                                    <div class="text-sm">{user.id}</div>
                                </div>
                                <!-- <div>
                                    <div class="text-sm text-gray-500">Employee ID</div>
                                    <div class="font-medium">{user.employee_id || '-'}</div>
                                </div> -->
                                <div>
                                    <div class="text-sm text-gray-500">{t('email')}</div>
                                    <div class="text-sm">{user.email}</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">{t('line_account')}</div>
                                    <div class="text-sm">{user.line_user_name || 'No LINE account'}</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">{t('last_active')}</div>
                                    <div class="text-sm">{displayDate(user.last_active).date}, {displayDate(user.last_active).time}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Work information -->
                    <div class="card bg-white border rounded-lg shadow-sm p-6 max-w-lg mx-auto">
                        <div class="header mb-4">
                            <div class="title text-xl font-semibold text-gray-900">{t('work_information')}</div>
                            <div class="subtext text-sm text-gray-600 mt-3">{t("view_users_work-related_memberships")}</div>
                        </div>

                        <div class="border-t pt-4 pb-4 border-gray-200">
                            <div class="grid grid-cols-1 gap-y-3 sm:grid-cols-1">
                                <div>
                                    <div class="text-sm text-gray-500">{t('role')}</div>
                                    <div class="text-sm">{user.roles}</div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">{t('partner')}</div>
                                    <div class="text-sm">
                                        {#if user.partners && user.partners.length}
                                            {user.partners.map(partner => partner.code).join(', ')}
                                        {:else}
                                            {t('no_partners')}
                                        {/if}
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">{t('department')}</div>
                                    <div class="text-sm">
                                        {#if user.departments && user.departments.length}
                                            {user.departments.map(dept => dept.code).join(', ')}
                                        {:else}
                                            {t('no_departments')}
                                        {/if}
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">{t('specialized_tags')}</div>
                                    <div class="text-sm">
                                        {#if user.tags && user.tags.length}
                                            {user.tags.map(dept => dept.name).join(', ')}
                                        {:else}
                                            {t('no_specialize_tags')}
                                        {/if}
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm text-gray-500">{t('current_workload')}</div>
                                    <div class="text-sm">{
                                    myTickets ? 
                                    myTickets.filter(ticket => ticket.status === 'assigned').length
                                    : 0} {t('tasks')}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right column - My Tasks (3/4 width) -->
            <div class="lg:col-span-4">
                <div class="rounded-lg border border-gray-200 bg-white">
                    <div class="border-b border-gray-200 p-4">
                        <div class="flex items-center gap-2">
                            <TicketOutline class="h-5 w-5 text-blue-600" />
                            <h2 class="text-lg font-medium text-gray-700">{t('my_tasks')}</h2>
                            <span class="ml-auto rounded-full bg-gray-100 px-2 py-1 text-sm font-medium text-gray-600">
                                {myTickets ? 
                                myTickets.filter(ticket => ticket.status === 'assigned').length
                                : 0}
                            </span>
                        </div>
                    </div>
                    
                    <div class="w-full">
                        <Table>
                            <TableHead>
                                <TableHeadCell>{t('table_no')}</TableHeadCell>
                                <TableHeadCell>{t('table_status')}</TableHeadCell>
                                <TableHeadCell>{t('table_priority')}</TableHeadCell>
                                <TableHeadCell>{t('table_sentiment')}</TableHeadCell>
                                <TableHeadCell>{t('table_customer')}</TableHeadCell>
                                <TableHeadCell>{t('table_time')}</TableHeadCell>
                                <TableHeadCell>{t('table_updated_on')}</TableHeadCell>
                            </TableHead>
                            <TableBody>
                                {#if myTickets && myTickets.length > 0}
                                    {#each myTickets as ticket}
                                        <TableBodyRow>
                                            <TableBodyCell>
                                                <a
                                                    href="/monitoring/{ticket.id}"
                                                    class="flex items-center justify-start text-blue-600 hover:underline py-2"
                                                >
                                                    {ticket.id}<EditSolid class="h-4 w-4" />
                                                </a>
                                            </TableBodyCell>
                                            <TableBodyCell>
                                                <div class="flex justify-start">
                                                    <span class={`${getStatusClass(ticket.status_id)} px-3 py-1 rounded-md text-sm w-32 text-center`}>
                                                        {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                                                    </span>
                                                </div>
                                            </TableBodyCell>
                                            <TableBodyCell>
                                                <div class="flex justify-start">  
                                                    <span class={`${getPriorityClass(ticket.priority.name)} p-2 rounded-md text-sm w-24`}>
                                                        {ticket.priority.name ?? "-"}
                                                    </span>
                                                </div>                        
                                            </TableBodyCell>

                                            <TableBodyCell>
                                                <div class="flex justify-center"> 
                                                    <div class={`flex items-center justify-center gap-1 rounded-md p-2 ${getSentimentClass(ticket.latest_analysis?.sentiment)}`}>
                                                        <img
                                                            src={getSentimentIcon(ticket.latest_analysis?.sentiment)}
                                                            alt={ticket.latest_analysis?.sentiment}
                                                            class="w-5 h-5"
                                                        />
                                                        <Tooltip>{ticket.latest_analysis?.sentiment ?? 'Unclassified'}</Tooltip>
                                                    </div>
                                                </div>
                                            </TableBodyCell>
                                            <TableBodyCell>
                                                {ticket.customer.name
                                                    ? ticket.customer.name
                                                    : ticket.customer.line_user.display_name}
                                            </TableBodyCell>

                                            <TableBodyCell>{timeAgo(ticket.updated_on)}</TableBodyCell>
                                            <TableBodyCell>
                                                <div class="text-sm">{displayDate(ticket.updated_on).date}</div>
                                                <div class="text-xs text-gray-500">{displayDate(ticket.updated_on).time}</div>
                                            </TableBodyCell>
                                        </TableBodyRow>
                                    {/each}
                                {:else}
                                        <div class="p-6 text-center text-gray-500">
                                            {t('no_tasks_assigned')}
                                        </div>
                                {/if}

                            </TableBody>
                        </Table>
                    </div>
                    
                    <!-- <div class="divide-y divide-gray-200">
                        {#if myTickets && myTickets.length > 0}
                            {#each myTickets as ticket}
                                <div class="p-4 hover:bg-gray-50">
                                    <div class="mb-2 flex items-center justify-between">
                                        <a href="/monitoring/{ticket.id}" class="font-medium text-blue-600 hover:underline">
                                            #{ticket.id}
                                        </a>
                                        <span 
                                            class:bg-gray-100={ticket.status === 'close'}
                                            class:text-gray-700={ticket.status === 'close'}
                                            class:bg-yellow-100={ticket.status === 'waiting to be assigned'}
                                            class:text-yellow-700={ticket.status === 'waiting to be assigned'}
                                            class:bg-green-100={ticket.status === 'open'}
                                            class:text-green-700={ticket.status === 'open'}
                                            class:bg-blue-100={ticket.status === 'assigned'}
                                            class:text-blue-700={ticket.status === 'assigned'}
                                            class="rounded-md px-2 py-1 text-xs font-medium"
                                        >
                                            {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
                                        </span>
                                    </div>
                                    <div class="mb-1 text-sm text-gray-900">
                                        {ticket.customer.name
                                            ? ticket.customer.name
                                            : ticket.customer.line_user.display_name}
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-2">
                                            <span class="rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-700">
                                                {ticket.priority.name}
                                            </span>
                                            {#if ticket.latest_analysis?.sentiment}
                                                {#if ticket.latest_analysis.sentiment === 'Positive'}
                                                    <span class="flex items-center rounded-md bg-green-100 px-2 py-1 text-xs font-medium text-green-700">
                                                        <span class="mr-1 h-2 w-2 rounded-full bg-green-500"></span>
                                                        {ticket.latest_analysis.sentiment}
                                                    </span>
                                                {:else if ticket.latest_analysis.sentiment === 'Negative'}
                                                    <span class="flex items-center rounded-md bg-red-100 px-2 py-1 text-xs font-medium text-red-700">
                                                        <span class="mr-1 h-2 w-2 rounded-full bg-red-500"></span>
                                                        {ticket.latest_analysis.sentiment}
                                                    </span>
                                                {:else}
                                                    <span class="flex items-center rounded-md bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-700">
                                                        <span class="mr-1 h-2 w-2 rounded-full bg-yellow-500"></span>
                                                        {ticket.latest_analysis.sentiment}
                                                    </span>
                                                {/if}
                                            {/if}
                                        </div>
                                        <div class="text-xs text-gray-500">{timeAgo(ticket.updated_on)}</div>
                                    </div>
                                </div>
                            {/each}
                        {:else}
                            <div class="p-6 text-center text-gray-500">
                                No tasks assigned
                            </div>
                        {/if}
                    </div> -->
                    
                    <!-- {#if myTickets && myTickets.length > 3}
                        <div class="border-t border-gray-200 p-4 text-center">
                            <a href="/my-tasks" class="text-sm font-medium text-blue-600 hover:underline">View all tasks</a>
                        </div>
                    {/if} -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .break-at-hyphen {
      word-break: break-word;
      overflow-wrap: break-word;
      hyphens: auto;
    }
</style>