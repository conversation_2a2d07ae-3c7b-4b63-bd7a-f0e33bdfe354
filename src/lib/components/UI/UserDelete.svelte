<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal, Label, Input, Checkbox, Alert } from 'flowbite-svelte';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { t } from '$src/lib/stores/i18n';

	export let user: any;

	let deleteForm: HTMLFormElement;
	let deleteModalOpen = false;
	let selectInstances: any = null;

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';

	function openDeleteModal(user: any) {
		selectInstances = { ...user };
		deleteModalOpen = true;
		// Reset messages when opening modal
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	function handleDeleteSubmit(event: Event) {
		// TODO - Delete this
		console.log(`handleDeleteSubmit's formData.username  - ${formData.confirm_username}`);
		console.log(`handleDeleteSubmit's user.username  - ${user.username}`);

		// Reset messages
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';

		// // Validate the filename matches
		// if (formData.username !== user.username) {
		//     event.preventDefault();
		//     showErrorMessage = true;
		//     errorMessage = 'Username does not match';
		//     return;
		// }
	}

	// TODO - Delete this if the new one is work
	// $: enhanceOptions = {
	//     modalOpen: deleteModalOpen,
	//     setModalOpen: (value: boolean) => deleteModalOpen = value,
	//     setSuccessMessage: (value: boolean) => showSuccessMessage = value,
	//     setErrorMessage: (value: boolean) => showErrorMessage = value,
	//     setErrorText: (value: string) => errorMessage = value
	// };

	$: enhanceOptions = {
		modalOpen: deleteModalOpen,
		setModalOpen: (value: boolean) => (deleteModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => (errorMessage = value)
	};

	// Form data with initial values from user
	let formData = {
		confirm_username: user.username
	};

	// TODO - Delete this
	// onst roles = ['Admin', 'Supervisor', 'Agent']; // Update based on your available roles
</script>

<Button
	color="none"
	class="w-full justify-start p-0 text-left text-red-600 hover:bg-gray-100"
	on:click={() => openDeleteModal(user)}
>
	{t('user_delete_user')}
</Button>

<Modal bind:open={deleteModalOpen} size="md" title={t('user_delete_user')}>
	<h2 slot="header" class="text-red-600">{t('user_delete_user')}</h2>
	{#if selectInstances}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<form
			bind:this={deleteForm}
			action="?/delete_user"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			class="space-y-4"
			on:submit={handleDeleteSubmit}
		>
			<input type="hidden" name="id" value={user.id} />
			<input type="hidden" name="username" value={user.username} />
			<div>
				<Label for="username" class="space-y-2 text-left">
					Type your username to confirm To confirm, type "{user.username}" in the box below
				</Label>
				<Input
					id="confirm_username"
					name="confirm_username"
					type="text"
					placeholder="Enter username to confirm"
					bind:value={formData.confirm_username}
					required
				/>
			</div>
		</form>
	{/if}
	<svelte:fragment slot="footer">
		<Button type="submit" color="blue" on:click={() => deleteForm.requestSubmit()}>Confirm</Button>
		<Button color="alternative" on:click={() => (deleteModalOpen = false)}>Cancel</Button>
	</svelte:fragment>
</Modal>
