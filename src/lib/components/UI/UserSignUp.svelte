<script lang="ts">
	import { t } from '$lib/stores/i18n';

	import { enhance } from '$app/forms';
	import { Button, Modal, Label, Input, Checkbox, Alert } from 'flowbite-svelte';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

	// export let user: any;

	let signUpForm: HTMLFormElement;
	let signUpModalOpen = false;
	// let selectInstances: any = null;

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {};

	// Flag to track if form should be reset when modal closes
	let shouldResetOnClose = true;

	// Function to dismiss all error alerts when user starts typing
	function dismissAlerts() {
		showErrorMessage = false;
		errorMessage = '';
		fieldErrors = {};
	}

	// Function to reset form to initial state
	function resetForm() {
		formData = {
			username: '',
			password: '',
			confirm_password: '',
			name: '',
			email: '',
			employee_id: String(count + 1),
			first_name: '',
			last_name: '',
			department: '',
			role: '',
			is_active: true,
			is_staff: true,
			is_superuser: false
		};
		passwordFieldsEverTyped = false;
	}

	// Combined handler for password input (existing functionality + alert dismissal)
	function handlePasswordInput() {
		passwordFieldsEverTyped = true;
		dismissAlerts();
	}

	// function openSignUpModal(user: any) {
	function openSignUpModal() {
		// selectInstances = { ...user };
		signUpModalOpen = true;
		// Reset messages when opening modal
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
		passwordFieldsEverTyped = false;
		// Reset the flag when opening modal
		shouldResetOnClose = true;
	}

	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	function handleSignUpSubmit(event: Event) {
		// Validate password matches confirm password
		// // TODO - Delete this
		// console.log(`handleSignUpSubmit's formData.password  - ${formData.password}`)
		// console.log(`handleSignUpSubmit's formData.confirm_password  - ${formData.confirm_password}`)

		// if (formData.password !== formData.confirm_password) {
		//     event.preventDefault();
		//     showErrorMessage = true;
		//     errorMessage = 'Passwords do not match';
		//     return false;
		// }

		// showSuccessMessage = false;
		// showErrorMessage = false;
		// successMessage = '';
		// errorMessage = '';

		// Reset messages
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};

		// // Check if passwords match
		// if (formData.password !== formData.confirm_password) {
		//     event.preventDefault();
		//     showErrorMessage = true;
		//     errorMessage = 'Passwords do not match';
		//     return ;
		// }

		return true;
	}

	// TODO - Delete this if the new one is work
	// $: enhanceOptions = {
	//     modalOpen: signUpModalOpen,
	//     setModalOpen: (value: boolean) => signUpModalOpen = value,
	//     setSuccessMessage: (value: boolean) => showSuccessMessage = value,
	//     setErrorMessage: (value: boolean) => showErrorMessage = value,
	//     setErrorText: (value: string) => errorMessage = value
	// };

	$: enhanceOptions = {
		modalOpen: signUpModalOpen,
		setModalOpen: (value: boolean) => {
			signUpModalOpen = value;
			// If modal is closing and we should reset form, do it now
			if (!value && shouldResetOnClose) {
				resetForm();
			}
		},
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => {
			successMessage = value;
			// Set flag to reset form when modal closes after successful submission
			shouldResetOnClose = true;
		},
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// New properties for enhanced success behavior
		useToastOnSuccess: true,
		closeModalOnSuccess: true
	};
	export let count: number;

	// Form data with initial values from user
	let formData = {
		username: '',
		password: '',
		confirm_password: '',
		name: '',
		email: '',
		// employee_id: "",
		employee_id: String(count + 1),
		first_name: '',
		last_name: '',
		department: '',
		role: '',
		is_active: true,
		is_staff: true,
		is_superuser: false
	};
	// TODO - Delete this
	// onst roles = ['Admin', 'Supervisor', 'Agent']; // Update based on your available roles

	// Password validation (similar to UserProfile)
	const specialChars = '!@#$%^&*';
	let passwordFieldsEverTyped = false;

	function checkPasswordRules(password: string) {
		return {
			length: password.length > 8,
			lowercase: /[a-z]/.test(password),
			uppercase: /[A-Z]/.test(password),
			special: new RegExp(`[${specialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(
				password
			),
			number: /[0-9]/.test(password)
		};
	}

	$: passwordRulesStatus = checkPasswordRules(formData.password);
	$: allPasswordRulesPassed = Object.values(passwordRulesStatus).every((value) => value === true);
	$: passwordsMatch =
		formData.password === formData.confirm_password && formData.password.length > 0;
</script>

<Button
	size="sm"
	class="bg-green-600 text-white hover:bg-green-700"
	on:click={() => openSignUpModal()}
>
	+ {t('new_account')}
</Button>

<Modal bind:open={signUpModalOpen} size="md" title="Sign-up User Information">
	<h3 slot="header">{t('create_account')}</h3>
	{#if showSuccessMessage}
		<Alert color="green" class="mb-4">
			{successMessage}
		</Alert>
	{/if}
	{#if showErrorMessage && errorMessage}
		<Alert color="red" class="mb-4">
			{errorMessage}
		</Alert>
	{/if}
	<form
		bind:this={signUpForm}
		action="?/sign_up_user"
		method="POST"
		use:enhance={() => handleEnhance(enhanceOptions)}
		class="space-y-4"
		on:submit={handleSignUpSubmit}
	>
		<div>
			<Label for="employee_id" class="space-y-2">
				{t('employee_id')}
			</Label>
			<Input
				id="employee_id"
				name="employee_id"
				type="text"
				bind:value={formData.employee_id}
				required
				disabled
				readonly
			/>
		</div>

		<div>
			<Label for="name" class="space-y-2">
				{t('nickname')}<span class="text-red-600">*</span>
			</Label>
			<Input
				id="name"
				name="name"
				type="text"
				bind:value={formData.name}
				on:input={dismissAlerts}
				required
			/>
		</div>

		<!-- <div>
            <Label for="first_name" class="space-y-2">First name</Label>
            <Input
                id="first_name"
                name="first_name"
                type="text"
                bind:value={formData.first_name}
                required
            />
        </div>

        <div>
            <Label for="last_name" class="space-y-2">Last name</Label>
            <Input
                id="last_name"
                name="last_name"
                type="text"
                bind:value={formData.last_name}
                required
            />
        </div> -->

		<div class="grid grid-cols-2 gap-4">
			<div>
				<Label for="first_name" class="space-y-2">
					{t('first_name')}<span class="text-red-600">*</span>
				</Label>
				<Input
					id="first_name"
					name="first_name"
					type="text"
					bind:value={formData.first_name}
					on:input={dismissAlerts}
					required
				/>
				{#if fieldErrors.first_name}
					{#each fieldErrors.first_name as error}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{error}
						</Alert>
					{/each}
				{/if}
			</div>

			<div>
				<Label for="last_name" class="space-y-2">
					{t('last_name')}<span class="text-red-600">*</span>
				</Label>
				<Input
					id="last_name"
					name="last_name"
					type="text"
					bind:value={formData.last_name}
					on:input={dismissAlerts}
					required
				/>
				{#if fieldErrors.last_name}
					{#each fieldErrors.last_name as error}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{error}
						</Alert>
					{/each}
				{/if}
			</div>
		</div>

		<div>
			<Label for="username" class="space-y-2">
				{t('username')}<span class="text-red-600">*</span>
			</Label>
			<Input
				id="username"
				name="username"
				type="text"
				bind:value={formData.username}
				on:input={dismissAlerts}
				required
			/>
			{#if fieldErrors.username}
				{#each fieldErrors.username as error}
					<Alert color="red" class="mt-1 px-3 py-2 text-sm">
						<!-- {error} -->
						{t('signup_error_duplicated_username')}
					</Alert>
				{/each}
			{/if}
		</div>

		<div>
			<Label for="email" class="space-y-2">
				{t('email')}<span class="text-red-600">*</span>
			</Label>
			<Input
				id="email"
				name="email"
				type="email"
				bind:value={formData.email}
				on:input={dismissAlerts}
				required
			/>
			{#if fieldErrors.email}
				{#each fieldErrors.email as error}
					<Alert color="red" class="mt-1 px-3 py-2 text-sm">
						<!-- {error} -->
						{t('signup_error_duplicated_email')}
					</Alert>
				{/each}
			{/if}
		</div>

		<div>
			<Label for="password" class="space-y-2">
				{t('password')}<span class="text-red-600">*</span>
			</Label>
			<Input
				id="password"
				name="password"
				type="password"
				bind:value={formData.password}
				on:input={handlePasswordInput}
				required
			/>
			<div>
				<div class="mb-1 mt-2 text-xs font-normal text-gray-400">
					{t('password_validation_msg_1')}
				</div>
				<ul class="space-y-0 text-xs">
					<li class="flex items-center">
						<span
							class={passwordRulesStatus.length
								? 'text-green-600'
								: passwordFieldsEverTyped
									? 'text-red-600'
									: 'text-gray-400'}>{t('password_validation_msg_2')}</span
						>
					</li>
					<li class="flex items-center">
						<span
							class={passwordRulesStatus.lowercase && passwordRulesStatus.uppercase
								? 'text-green-600'
								: passwordFieldsEverTyped
									? 'text-red-600'
									: 'text-gray-400'}>{t('password_validation_msg_3')}</span
						>
					</li>
					<li class="flex items-center">
						<span
							class={passwordRulesStatus.number
								? 'text-green-600'
								: passwordFieldsEverTyped
									? 'text-red-600'
									: 'text-gray-400'}>{t('password_validation_msg_4')}</span
						>
					</li>
					<li class="flex items-center">
						<span
							class={passwordRulesStatus.special
								? 'text-green-600'
								: passwordFieldsEverTyped
									? 'text-red-600'
									: 'text-gray-400'}>{t('password_validation_msg_5')} ({specialChars})</span
						>
					</li>
				</ul>
			</div>
			{#if fieldErrors.password}
				{#each fieldErrors.password as error}
					<Alert color="red" class="mt-1 px-3 py-2 text-sm">
						{error}
					</Alert>
				{/each}
			{/if}
		</div>

		<div>
			<Label for="confirm_password" class="space-y-2">
				{t('confirm_password')}<span class="text-red-600">*</span>
			</Label>
			<Input
				id="confirm_password"
				name="confirm_password"
				type="password"
				bind:value={formData.confirm_password}
				on:input={handlePasswordInput}
				required
			/>
			<div style="min-height:1em;" class="justify-left items-top mt-2 flex">
				{#if passwordFieldsEverTyped && !passwordsMatch && formData.confirm_password.length > 0}
					<span class="text-left text-xs text-red-600"
						>{t('password_validation_msg_do_not_match')}</span
					>
				{/if}
			</div>
			{#if fieldErrors.confirm_password}
				{#each fieldErrors.confirm_password as error}
					<Alert color="red" class="mt-1 px-3 py-2 text-sm">
						{error}
					</Alert>
				{/each}
			{/if}
		</div>

		<!-- <div>
            <Label for="department" class="space-y-2">Department</Label>
            <Input
                id="department"
                name="department"
                type="text"
                bind:value={formData.department}
            />
        </div>

        <div>
            <Label for="role" class="space-y-2">Role</Label>
            <Select 
                id="role" 
                name="role" 
                bind:value={formData.role}
                required
            >
                {#each roles as role}
                    <option value={role}>{role}</option>
                {/each}
            </Select>
        </div> -->

		<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_active} bind:value={formData.is_active}> -->
		<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_active}>
            Enable Active Status
        </Checkbox>            
        <input type="hidden" name="is_active" value={formData.is_active}> -->

		<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_staff} bind:value={formData.is_staff}> -->
		<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_staff}>
            Enable Staff
        </Checkbox>                 -->
		<!-- <input type="hidden" name="is_staff" value={formData.is_staff}> -->

		<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_superuser} bind:value={formData.is_superuser}> -->
		<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_superuser}>
            Enable Superuser
        </Checkbox>                 
        <input type="hidden" name="is_superuser" value={formData.is_superuser}> -->

		<input type="hidden" name="is_active" value="true" />
		<input type="hidden" name="is_staff" value="true" />
		<input type="hidden" name="is_superuser" value="false" />
	</form>
	<svelte:fragment slot="footer">
		<Button
			type="submit"
			color="blue"
			disabled={!allPasswordRulesPassed || !passwordsMatch}
			on:click={() => signUpForm.requestSubmit()}
		>
			{t('submit')}
		</Button>
		<Button color="alternative" on:click={() => (signUpModalOpen = false)}>{t('cancel')}</Button>
	</svelte:fragment>
</Modal>
